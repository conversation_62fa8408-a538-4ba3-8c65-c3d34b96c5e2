"""
Subscription Middleware

This middleware checks if B2C users have valid, non-expired subscriptions
and returns 402 Payment Required if their plan has expired.
"""

from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from app.connection.establish_db_connection import get_mongo_db
from app.connection.tenant_middleware import KAVIA_ROOT_DB_NAME, get_tenant_id
from app.core.Settings import settings
from app.utils.auth_utils import get_current_user_from_request
import logging
from datetime import datetime

class SubscriptionMiddleware(BaseHTTPMiddleware):
    """
    Middleware to check subscription status for B2C users
    
    For B2C tenant users, this middleware:
    1. Checks if user has an active subscription
    2. Verifies the subscription hasn't expired
    3. Returns 402 Payment Required if subscription is expired
    4. Allows request to continue if subscription is valid
    """
    
    # Routes that should be excluded from subscription checks
    EXCLUDED_ROUTES = {
        "/health",
        "/docs",
        "/openapi.json",
        "/redoc",
        "/payment/",  # Payment routes should be accessible
        "/auth/",     # Authentication routes
        "/manage/super/",  # Super admin routes
    }
    
    def __init__(self, app):
        super().__init__(app)
        self.mongo_db = None
    
    def _get_mongo_db(self):
        """Get MongoDB connection (lazy initialization)"""
        if not self.mongo_db:
            self.mongo_db = get_mongo_db(db_name=KAVIA_ROOT_DB_NAME)
        return self.mongo_db
    
    def _should_check_subscription(self, request: Request) -> bool:
        """
        Determine if subscription check should be performed
        
        Args:
            request: FastAPI request object
            
        Returns:
            bool: True if subscription check should be performed
        """
        # Check if route is excluded
        path = request.url.path
        for excluded_route in self.EXCLUDED_ROUTES:
            if path.startswith(excluded_route):
                return False
        
        # Only check for B2C tenant
        try:
            tenant_id = get_tenant_id()
            return tenant_id == settings.KAVIA_B2C_CLIENT_ID
        except:
            return False
    
    def _get_user_from_request(self, request: Request) -> str:
        """
        Extract user ID from request
        
        Args:
            request: FastAPI request object
            
        Returns:
            str: User ID or None if not found
        """
        try:
            # Try to get user from JWT token or session
            user = get_current_user_from_request(request)
            if user:
                return user.get("cognito:username") or user.get("user_id")
        except:
            pass
        
        return None
    
    def _check_subscription_status(self, user_id: str, tenant_id: str) -> dict:
        """
        Check user's subscription status
        
        Args:
            user_id: User ID
            tenant_id: Tenant ID
            
        Returns:
            dict: Subscription status information
        """
        try:
            mongo_db = self._get_mongo_db()
            
            # Get latest active subscription for user
            subscription = mongo_db.db["active_subscriptions"].find_one(
                {"user_id": user_id, "tenant_id": tenant_id},
                sort=[("created_at", -1)]
            )
            
            if not subscription:
                return {
                    "valid": False,
                    "reason": "no_subscription",
                    "message": "No active subscription found"
                }
            
            # Check if subscription is marked as expired
            if subscription.get("expired_plan", False):
                return {
                    "valid": False,
                    "reason": "expired_plan",
                    "message": "Your subscription has expired. Please upgrade your plan to continue.",
                    "expires_at": subscription.get("expires_at"),
                    "subscription_id": subscription.get("subscription_id")
                }
            
            # Check if subscription has passed expiry date
            expires_at = subscription.get("expires_at")
            if expires_at:
                try:
                    from app.utils.datetime_utils import from_isoformat
                    expiry_date = from_isoformat(expires_at)
                    current_date = datetime.utcnow()
                    
                    if current_date > expiry_date:
                        # Mark as expired in database
                        mongo_db.db["active_subscriptions"].update_one(
                            {"_id": subscription["_id"]},
                            {"$set": {"expired_plan": True}}
                        )
                        
                        return {
                            "valid": False,
                            "reason": "expired_date",
                            "message": "Your subscription has expired. Please upgrade your plan to continue.",
                            "expires_at": expires_at,
                            "subscription_id": subscription.get("subscription_id")
                        }
                except Exception as e:
                    logging.warning(f"Error parsing expiry date: {e}")
            
            # Subscription is valid
            return {
                "valid": True,
                "subscription": subscription
            }
            
        except Exception as e:
            logging.error(f"Error checking subscription status: {e}")
            # In case of error, allow request to continue (fail open)
            return {"valid": True, "error": str(e)}
    
    async def dispatch(self, request: Request, call_next):
        """
        Main middleware logic
        
        Args:
            request: FastAPI request object
            call_next: Next middleware/route handler
            
        Returns:
            Response object
        """
        # Check if subscription validation is needed
        if not self._should_check_subscription(request):
            return await call_next(request)
        
        # Get user ID from request
        user_id = self._get_user_from_request(request)
        if not user_id:
            # No user found, let the request continue (auth middleware will handle)
            return await call_next(request)
        
        # Check subscription status
        tenant_id = get_tenant_id()
        subscription_status = self._check_subscription_status(user_id, tenant_id)
        
        if not subscription_status["valid"]:
            # Return 402 Payment Required
            return JSONResponse(
                status_code=402,
                content={
                    "error": "payment_required",
                    "message": subscription_status["message"],
                    "reason": subscription_status["reason"],
                    "user_id": user_id,
                    "tenant_id": tenant_id,
                    "expires_at": subscription_status.get("expires_at"),
                    "subscription_id": subscription_status.get("subscription_id"),
                    "action_required": "Please upgrade your subscription to continue using the service."
                }
            )
        
        # Subscription is valid, continue with request
        return await call_next(request)

# Helper function to add middleware to FastAPI app
def add_subscription_middleware(app):
    """
    Add subscription middleware to FastAPI application
    
    Args:
        app: FastAPI application instance
    """
    app.add_middleware(SubscriptionMiddleware)
    logging.info("Subscription middleware added to application")
