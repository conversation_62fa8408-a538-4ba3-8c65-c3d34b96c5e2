# 🔄 Subscription Expiry Management System

This document outlines the complete subscription expiry management system that automatically handles plan expiration, notifications, and access restrictions.

## 🎯 **System Overview**

The system consists of 4 main components:

1. **EventBridge Scheduler** - Triggers notifications 1 day before expiry
2. **Lambda Function** - <PERSON>les expiry notifications and marking plans as expired
3. **Subscription Middleware** - Checks subscription status on every request
4. **Database Updates** - Enhanced subscription schema with expiry tracking

## 📊 **Flow Diagram**

```
User Purchases Plan → EventBridge Scheduler Created → 1 Day Before Expiry
                                                            ↓
                                                    Lambda Triggered
                                                            ↓
                                              Send Email Notification
                                                            ↓
                                              Schedule Expiry Marking
                                                            ↓
                                          Plan Expires → Mark as expired_plan: true
                                                            ↓
                                          User Makes Request → Middleware Check
                                                            ↓
                                              Return 402 Payment Required
```

## 🏗️ **Implementation Details**

### **1. Enhanced Subscription Schema**

Updated `active_subscriptions` collection with new fields:

```javascript
{
  "subscription_id": "sub_123",
  "user_id": "user_456", 
  "tenant_id": "b2c",
  "expires_at": "2025-08-07T10:30:00Z",
  "expired_plan": false,              // NEW: Tracks if plan is expired
  "expiry_notification_sent": false,  // NEW: Tracks if notification sent
  "scheduler_rule_arn": "arn:aws:...", // NEW: EventBridge rule ARN
  // ... other existing fields
}
```

### **2. EventBridge Scheduler Creation**

When a user purchases a plan, the system:

1. **Calculates notification date** (1 day before expiry)
2. **Creates EventBridge rule** with cron expression
3. **Sets Lambda as target** with subscription details
4. **Stores rule ARN** in subscription document

```python
# Example cron expression for July 6, 2025 at 10:30 AM
cron_expression = "cron(30 10 6 7 ? 2025)"
```

### **3. Lambda Function Workflow**

The Lambda function handles two types of events:

#### **A. Expiry Warning (1 day before)**
- Sends email notification to user
- Marks `expiry_notification_sent: true`
- Schedules another event for actual expiry

#### **B. Mark as Expired (on expiry date)**
- Updates `expired_plan: true` in database
- Cleans up EventBridge rules

### **4. Subscription Middleware**

Added to `main.py` middleware stack:

```python
_app.add_middleware(SubscriptionMiddleware)
```

**Middleware Logic:**
1. **Check if B2C user** - Only applies to B2C tenant
2. **Extract user ID** from JWT token
3. **Query subscription status** from database
4. **Return 402** if `expired_plan: true`
5. **Allow request** if subscription is valid

**Excluded Routes:**
- `/health`, `/docs`, `/auth/`, `/payment/`, `/manage/super/`

## 📁 **Files Modified/Created**

### **Modified Files:**
1. **`app/routes/payment_route.py`**
   - Enhanced subscription template with new fields
   - Added EventBridge scheduler creation
   - Integrated with existing Stripe payment flow

2. **`app/main.py`**
   - Added SubscriptionMiddleware to middleware stack
   - Imported subscription middleware

3. **`app/utils/auth_utils.py`**
   - Added `get_current_user_from_request()` helper function

### **New Files:**
1. **`app/middleware/subscription_middleware.py`**
   - Complete middleware implementation
   - Subscription status checking logic
   - 402 error response handling

2. **`lambda/subscription_expiry_handler.py`**
   - Lambda function for handling expiry events
   - Email notification sending
   - Database updates for expired plans

## 🔧 **Configuration Requirements**

### **Environment Variables (Lambda):**
```bash
MONGO_URI=************************:port/admin
DB_NAME=develop_kaviaroot
AWS_REGION=us-east-1
AWS_ACCOUNT_ID=************
SES_FROM_EMAIL=<EMAIL>
```

### **AWS Permissions Required:**
- **EventBridge**: `events:PutRule`, `events:PutTargets`
- **Lambda**: `lambda:InvokeFunction`
- **SES**: `ses:SendEmail`
- **MongoDB**: Read/Write access to collections

## 📧 **Email Notification Template**

```
Subject: ⚠️ Your Kavia AI subscription expires tomorrow

Dear [User Name],

This is a friendly reminder that your Kavia AI subscription will expire tomorrow.

Subscription Details:
- Subscription ID: sub_123
- Expiry Date: July 7, 2025 at 10:30 AM UTC
- Current Plan: Premium Advanced

To avoid any interruption in service, please upgrade your subscription before it expires.

Best regards,
The Kavia AI Team
```

## 🚨 **Error Response (402 Payment Required)**

When a user with expired subscription makes a request:

```json
{
  "error": "payment_required",
  "message": "Your subscription has expired. Please upgrade your plan to continue.",
  "reason": "expired_plan",
  "user_id": "user_456",
  "tenant_id": "b2c",
  "expires_at": "2025-07-07T10:30:00Z",
  "subscription_id": "sub_123",
  "action_required": "Please upgrade your subscription to continue using the service."
}
```

## 🧪 **Testing the System**

### **1. Test Plan Purchase:**
```bash
# Create a subscription with short expiry for testing
POST /api/payment/success
{
  "internal_secret_token": "test_token"
}
```

### **2. Test Middleware:**
```bash
# Make request with expired user
GET /api/any-endpoint
Authorization: Bearer <expired_user_token>

# Expected: 402 Payment Required
```

### **3. Test Lambda Function:**
```bash
# Manually trigger Lambda with test event
{
  "user_id": "test_user",
  "tenant_id": "b2c", 
  "subscription_id": "test_sub",
  "expires_at": "2025-07-07T10:30:00Z",
  "notification_type": "expiry_warning"
}
```

## 🔄 **Deployment Steps**

1. **Deploy Lambda Function:**
   ```bash
   # Package and deploy subscription_expiry_handler.py
   aws lambda create-function --function-name subscription-expiry-handler
   ```

2. **Update Backend:**
   ```bash
   # Deploy updated payment_route.py and middleware
   docker build -t kavia-backend .
   ```

3. **Test Integration:**
   ```bash
   # Verify EventBridge rules are created
   # Test middleware responses
   # Confirm email notifications
   ```

## 💡 **Benefits**

1. **Automated Expiry Management** - No manual intervention required
2. **Proactive Notifications** - Users warned 1 day before expiry
3. **Immediate Access Control** - Expired users blocked instantly
4. **Audit Trail** - Complete tracking of expiry events
5. **Scalable Architecture** - Handles thousands of subscriptions
6. **Cost Effective** - Only triggers when needed

## 🔒 **Security Considerations**

1. **JWT Validation** - Middleware validates user tokens
2. **Database Security** - Secure MongoDB connections
3. **AWS IAM** - Least privilege access for Lambda
4. **Error Handling** - Graceful degradation on failures
5. **Rate Limiting** - Prevent abuse of payment endpoints

This system provides a complete, automated solution for subscription expiry management with proactive notifications and immediate access control! 🎯
