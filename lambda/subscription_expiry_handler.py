"""
AWS Lambda Function: Subscription Expiry Handler (Develop Stage - MongoDB Testing)

This Lambda function is triggered by EventBridge scheduler to:
1. Send expiry warning notifications (1 day before expiry) - SNS COMMENTED FOR TESTING
2. Mark subscriptions as expired when they expire
3. Test MongoDB operations first, then add SNS later
"""

import json
import boto3
import logging
from datetime import datetime, timedelta
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
STAGE = os.environ.get('STAGE', 'develop')  # develop, qa, pre_prod
AWS_REGION = os.environ.get('AWS_REGION', 'us-east-1')
# SNS_TOPIC_ARN = os.environ.get('SNS_TOPIC_ARN')  # COMMENTED FOR TESTING

# Testing mode - set to False to test actual MongoDB operations
TESTING_MODE = os.environ.get('TESTING_MODE', 'false').lower() == 'true'

def get_environment_config(stage):
    """
    Get MongoDB configuration based on stage

    Args:
        stage: Environment stage (develop, qa, pre_prod)

    Returns:
        dict: MongoDB URI and database name for the stage
    """
    configs = {
        'develop': {
            'mongo_uri': os.environ.get('MONGO_URI_DEV', '**************************************************************'),
            'db_name': 'develop_kaviaroot'
        },
        'qa': {
            'mongo_uri': os.environ.get('MONGO_URI_QA', '******************************************************************'),
            'db_name': 'qa_kaviaroot'
        },
        'pre_prod': {
            'mongo_uri': os.environ.get('MONGO_URI_BETA', '********************************************************************'),
            'db_name': 'pre_prod_kaviaroot'
        }
    }

    return configs.get(stage, configs['develop'])  # Default to develop if stage not found

# Get current environment configuration
ENV_CONFIG = get_environment_config(STAGE)
MONGO_URI = ENV_CONFIG['mongo_uri']
DB_NAME = ENV_CONFIG['db_name']

def lambda_handler(event, context):
    """
    Main Lambda handler function

    Args:
        event: EventBridge event data
        context: Lambda context

    Returns:
        dict: Response with status and message
    """
    try:
        logger.info(f"Received event: {json.dumps(event)}")

        # Parse event data
        user_id = event.get('user_id', 'test-user')
        tenant_id = event.get('tenant_id', 'b2c')
        subscription_id = event.get('subscription_id', 'test-sub')
        expires_at = event.get('expires_at', '2025-07-08T10:30:00Z')
        notification_type = event.get('notification_type', 'expiry_warning')

        logger.info(f"Processing: user_id={user_id}, notification_type={notification_type}")

        # TESTING MODE - Skip MongoDB and SNS
        if TESTING_MODE:
            logger.info("🧪 TESTING MODE: Skipping MongoDB and SNS")

            test_result = {
                'status': 'test_success',
                'message': f'Test successful for {notification_type}',
                'user_id': user_id,
                'subscription_id': subscription_id,
                'testing_mode': True,
                'environment': {
                    'stage': STAGE,
                    'db_name': DB_NAME,
                    'mongo_uri_masked': MONGO_URI[:20] + '***' if MONGO_URI else 'Not set',
                    'sns_topic_arn': 'COMMENTED FOR TESTING'
                },
                'would_send_notification': notification_type == 'expiry_warning',
                'would_mark_expired': notification_type == 'mark_expired'
            }

            return {
                'statusCode': 200,
                'body': json.dumps(test_result)
            }

        # PRODUCTION MODE - Full functionality
        try:
            from pymongo import MongoClient
        except ImportError:
            logger.error("pymongo not available - add MongoDB layer to Lambda")
            return {
                'statusCode': 500,
                'body': json.dumps({
                    'error': 'pymongo not available',
                    'message': 'Add MongoDB layer to Lambda function'
                })
            }

        # Connect to MongoDB
        mongo_client = MongoClient(MONGO_URI)
        db = mongo_client[DB_NAME]

        try:
            if notification_type == 'expiry_warning':
                # Send expiry warning notification
                result = send_expiry_warning(
                    db, user_id, tenant_id, subscription_id, expires_at
                )

                # Schedule actual expiry marking for tomorrow
                schedule_expiry_marking(
                    user_id, tenant_id, subscription_id, expires_at
                )

            elif notification_type == 'mark_expired':
                # Mark subscription as expired
                result = mark_subscription_expired(
                    db, user_id, tenant_id, subscription_id
                )

            else:
                raise ValueError(f"Unknown notification type: {notification_type}")

            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'Successfully processed subscription expiry event',
                    'user_id': user_id,
                    'subscription_id': subscription_id,
                    'notification_type': notification_type,
                    'result': result
                })
            }

        finally:
            mongo_client.close()

    except Exception as e:
        logger.error(f"Error processing subscription expiry: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': 'Failed to process subscription expiry',
                'message': str(e)
            })
        }

def send_expiry_warning(db, user_id: str, tenant_id: str, subscription_id: str, expires_at: str):
    """
    Send expiry warning notification to user
    
    Args:
        db: MongoDB database connection
        user_id: User ID
        tenant_id: Tenant ID
        subscription_id: Subscription ID
        expires_at: Expiry date string
        
    Returns:
        dict: Result of notification sending
    """
    try:
        # Get user details
        user = db["users"].find_one({"_id": user_id})
        if not user:
            logger.warning(f"User {user_id} not found")
            return {"status": "user_not_found"}
        
        user_email = user.get("email")
        user_name = user.get("name", user_email)
        
        # Get subscription details
        subscription = db["active_subscriptions"].find_one({"subscription_id": subscription_id})
        if not subscription:
            logger.warning(f"Subscription {subscription_id} not found")
            return {"status": "subscription_not_found"}
        
        # Parse expiry date
        expiry_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
        formatted_expiry = expiry_date.strftime("%B %d, %Y at %I:%M %p UTC")
        
        # NOTIFICATION LOGIC - SNS COMMENTED FOR TESTING
        notification_subject = "⚠️ Your Kavia AI subscription expires tomorrow"
        notification_message = f"""
Dear {user_name},

This is a friendly reminder that your Kavia AI subscription will expire tomorrow.

Subscription Details:
- Subscription ID: {subscription_id}
- Expiry Date: {formatted_expiry}
- Current Plan: {subscription.get('price_id', 'Unknown')}
- User Email: {user_email}

To avoid any interruption in service, please upgrade your subscription before it expires.

You can upgrade your plan by visiting your account dashboard or contacting our support team.

Thank you for using Kavia AI!

Best regards,
The Kavia AI Team
        """

        logger.info(f"📧 WOULD SEND NOTIFICATION TO: {user_email}")
        logger.info(f"📧 SUBJECT: {notification_subject}")
        logger.info(f"📧 MESSAGE: {notification_message}")

        # TODO: Uncomment when SNS is ready
        # sns_client = boto3.client('sns', region_name=AWS_REGION)
        # sns_topic_arn = os.environ.get('SNS_TOPIC_ARN')
        # sns_response = sns_client.publish(
        #     TopicArn=sns_topic_arn,
        #     Subject=notification_subject,
        #     Message=notification_message,
        #     MessageAttributes={
        #         'user_id': {'DataType': 'String', 'StringValue': user_id},
        #         'user_email': {'DataType': 'String', 'StringValue': user_email},
        #         'subscription_id': {'DataType': 'String', 'StringValue': subscription_id},
        #         'notification_type': {'DataType': 'String', 'StringValue': 'expiry_warning'}
        #     }
        # )
        
        # Update subscription to mark notification as sent
        update_result = db["active_subscriptions"].update_one(
            {"subscription_id": subscription_id},
            {"$set": {
                "expiry_notification_sent": True,
                "notification_sent_at": datetime.utcnow().isoformat()
            }}
        )

        logger.info(f"✅ Expiry warning processed for {user_email}, subscription {subscription_id}")
        logger.info(f"✅ Database updated: {update_result.modified_count} documents modified")

        return {
            "status": "notification_sent",
            "user_id": user_id,
            "user_email": user_email,
            "subscription_id": subscription_id,
            "notification_method": "LOGGED_ONLY_SNS_COMMENTED",
            "database_updated": update_result.modified_count > 0,
            "modified_count": update_result.modified_count
        }
        
    except Exception as e:
        logger.error(f"Error sending expiry warning: {str(e)}")
        return {"status": "error", "message": str(e)}

def mark_subscription_expired(db, user_id: str, tenant_id: str, subscription_id: str):
    """
    Mark subscription as expired in database
    
    Args:
        db: MongoDB database connection
        user_id: User ID
        tenant_id: Tenant ID
        subscription_id: Subscription ID
        
    Returns:
        dict: Result of marking subscription as expired
    """
    try:
        # Update subscription to mark as expired
        result = db["active_subscriptions"].update_one(
            {"subscription_id": subscription_id},
            {"$set": {
                "expired_plan": True,
                "expired_at": datetime.utcnow().isoformat()
            }}
        )
        
        if result.modified_count > 0:
            logger.info(f"Marked subscription {subscription_id} as expired for user {user_id}")
            return {"status": "marked_expired", "modified_count": result.modified_count}
        else:
            logger.warning(f"No subscription found to mark as expired: {subscription_id}")
            return {"status": "subscription_not_found"}
            
    except Exception as e:
        logger.error(f"Error marking subscription as expired: {str(e)}")
        return {"status": "error", "message": str(e)}

def schedule_expiry_marking(user_id: str, tenant_id: str, subscription_id: str, expires_at: str):
    """
    Schedule EventBridge rule to mark subscription as expired
    
    Args:
        user_id: User ID
        tenant_id: Tenant ID
        subscription_id: Subscription ID
        expires_at: Expiry date string
    """
    try:
        eventbridge_client = boto3.client('events', region_name=AWS_REGION)
        
        # Parse expiry date and add a few minutes buffer
        expiry_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
        marking_date = expiry_date + timedelta(minutes=5)  # Mark 5 minutes after expiry
        
        # Create rule name
        rule_name = f"mark-expired-{subscription_id}"
        
        # Create cron expression
        cron_expression = f"cron({marking_date.minute} {marking_date.hour} {marking_date.day} {marking_date.month} ? {marking_date.year})"
        
        # Create EventBridge rule
        eventbridge_client.put_rule(
            Name=rule_name,
            ScheduleExpression=cron_expression,
            Description=f"Mark subscription {subscription_id} as expired",
            State='ENABLED'
        )
        
        # Add target
        lambda_function_arn = f"arn:aws:lambda:{AWS_REGION}:{os.environ.get('AWS_ACCOUNT_ID')}:function:subscription-expiry-handler"
        
        eventbridge_client.put_targets(
            Rule=rule_name,
            Targets=[
                {
                    'Id': '1',
                    'Arn': lambda_function_arn,
                    'Input': json.dumps({
                        'user_id': user_id,
                        'tenant_id': tenant_id,
                        'subscription_id': subscription_id,
                        'expires_at': expires_at,
                        'notification_type': 'mark_expired'
                    })
                }
            ]
        )
        
        logger.info(f"Scheduled expiry marking for subscription {subscription_id}")
        
    except Exception as e:
        logger.error(f"Error scheduling expiry marking: {str(e)}")
        # Don't raise exception as this is not critical
