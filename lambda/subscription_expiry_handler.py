"""
AWS Lambda Function: Subscription Expiry Handler

This Lambda function is triggered by EventBridge scheduler to:
1. Send expiry warning notifications (1 day before expiry)
2. Mark subscriptions as expired when they expire
3. Clean up EventBridge rules after processing
"""

import json
import boto3
import logging
from datetime import datetime, timedelta
from pymongo import MongoClient
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
MONGO_URI = os.environ.get('MONGO_URI')
DB_NAME = os.environ.get('DB_NAME', 'develop_kaviaroot')
AWS_REGION = os.environ.get('AWS_REGION', 'us-east-1')
SES_FROM_EMAIL = os.environ.get('SES_FROM_EMAIL', '<EMAIL>')

def lambda_handler(event, context):
    """
    Main Lambda handler function
    
    Args:
        event: EventBridge event data
        context: Lambda context
        
    Returns:
        dict: Response with status and message
    """
    try:
        logger.info(f"Received event: {json.dumps(event)}")
        
        # Parse event data
        user_id = event['user_id']
        tenant_id = event['tenant_id']
        subscription_id = event['subscription_id']
        expires_at = event['expires_at']
        notification_type = event['notification_type']
        
        # Connect to MongoDB
        mongo_client = MongoClient(MONGO_URI)
        db = mongo_client[DB_NAME]
        
        try:
            if notification_type == 'expiry_warning':
                # Send expiry warning notification
                result = send_expiry_warning(
                    db, user_id, tenant_id, subscription_id, expires_at
                )
                
                # Schedule actual expiry marking for tomorrow
                schedule_expiry_marking(
                    user_id, tenant_id, subscription_id, expires_at
                )
                
            elif notification_type == 'mark_expired':
                # Mark subscription as expired
                result = mark_subscription_expired(
                    db, user_id, tenant_id, subscription_id
                )
                
            else:
                raise ValueError(f"Unknown notification type: {notification_type}")
            
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'message': 'Successfully processed subscription expiry event',
                    'user_id': user_id,
                    'subscription_id': subscription_id,
                    'notification_type': notification_type,
                    'result': result
                })
            }
            
        finally:
            mongo_client.close()
            
    except Exception as e:
        logger.error(f"Error processing subscription expiry: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': 'Failed to process subscription expiry',
                'message': str(e)
            })
        }

def send_expiry_warning(db, user_id: str, tenant_id: str, subscription_id: str, expires_at: str):
    """
    Send expiry warning notification to user
    
    Args:
        db: MongoDB database connection
        user_id: User ID
        tenant_id: Tenant ID
        subscription_id: Subscription ID
        expires_at: Expiry date string
        
    Returns:
        dict: Result of notification sending
    """
    try:
        # Get user details
        user = db["users"].find_one({"_id": user_id})
        if not user:
            logger.warning(f"User {user_id} not found")
            return {"status": "user_not_found"}
        
        user_email = user.get("email")
        user_name = user.get("name", user_email)
        
        # Get subscription details
        subscription = db["active_subscriptions"].find_one({"subscription_id": subscription_id})
        if not subscription:
            logger.warning(f"Subscription {subscription_id} not found")
            return {"status": "subscription_not_found"}
        
        # Parse expiry date
        expiry_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
        formatted_expiry = expiry_date.strftime("%B %d, %Y at %I:%M %p UTC")
        
        # Send email notification using SES
        ses_client = boto3.client('ses', region_name=AWS_REGION)
        
        email_subject = "⚠️ Your Kavia AI subscription expires tomorrow"
        email_body = f"""
        Dear {user_name},
        
        This is a friendly reminder that your Kavia AI subscription will expire tomorrow.
        
        Subscription Details:
        - Subscription ID: {subscription_id}
        - Expiry Date: {formatted_expiry}
        - Current Plan: {subscription.get('price_id', 'Unknown')}
        
        To avoid any interruption in service, please upgrade your subscription before it expires.
        
        You can upgrade your plan by visiting your account dashboard or contacting our support team.
        
        Thank you for using Kavia AI!
        
        Best regards,
        The Kavia AI Team
        """
        
        ses_response = ses_client.send_email(
            Source=SES_FROM_EMAIL,
            Destination={'ToAddresses': [user_email]},
            Message={
                'Subject': {'Data': email_subject},
                'Body': {'Text': {'Data': email_body}}
            }
        )
        
        # Update subscription to mark notification as sent
        db["active_subscriptions"].update_one(
            {"subscription_id": subscription_id},
            {"$set": {"expiry_notification_sent": True}}
        )
        
        logger.info(f"Expiry warning sent to {user_email} for subscription {subscription_id}")
        
        return {
            "status": "notification_sent",
            "email": user_email,
            "ses_message_id": ses_response['MessageId']
        }
        
    except Exception as e:
        logger.error(f"Error sending expiry warning: {str(e)}")
        return {"status": "error", "message": str(e)}

def mark_subscription_expired(db, user_id: str, tenant_id: str, subscription_id: str):
    """
    Mark subscription as expired in database
    
    Args:
        db: MongoDB database connection
        user_id: User ID
        tenant_id: Tenant ID
        subscription_id: Subscription ID
        
    Returns:
        dict: Result of marking subscription as expired
    """
    try:
        # Update subscription to mark as expired
        result = db["active_subscriptions"].update_one(
            {"subscription_id": subscription_id},
            {"$set": {
                "expired_plan": True,
                "expired_at": datetime.utcnow().isoformat()
            }}
        )
        
        if result.modified_count > 0:
            logger.info(f"Marked subscription {subscription_id} as expired for user {user_id}")
            return {"status": "marked_expired", "modified_count": result.modified_count}
        else:
            logger.warning(f"No subscription found to mark as expired: {subscription_id}")
            return {"status": "subscription_not_found"}
            
    except Exception as e:
        logger.error(f"Error marking subscription as expired: {str(e)}")
        return {"status": "error", "message": str(e)}

def schedule_expiry_marking(user_id: str, tenant_id: str, subscription_id: str, expires_at: str):
    """
    Schedule EventBridge rule to mark subscription as expired
    
    Args:
        user_id: User ID
        tenant_id: Tenant ID
        subscription_id: Subscription ID
        expires_at: Expiry date string
    """
    try:
        eventbridge_client = boto3.client('events', region_name=AWS_REGION)
        
        # Parse expiry date and add a few minutes buffer
        expiry_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
        marking_date = expiry_date + timedelta(minutes=5)  # Mark 5 minutes after expiry
        
        # Create rule name
        rule_name = f"mark-expired-{subscription_id}"
        
        # Create cron expression
        cron_expression = f"cron({marking_date.minute} {marking_date.hour} {marking_date.day} {marking_date.month} ? {marking_date.year})"
        
        # Create EventBridge rule
        eventbridge_client.put_rule(
            Name=rule_name,
            ScheduleExpression=cron_expression,
            Description=f"Mark subscription {subscription_id} as expired",
            State='ENABLED'
        )
        
        # Add target
        lambda_function_arn = f"arn:aws:lambda:{AWS_REGION}:{os.environ.get('AWS_ACCOUNT_ID')}:function:subscription-expiry-handler"
        
        eventbridge_client.put_targets(
            Rule=rule_name,
            Targets=[
                {
                    'Id': '1',
                    'Arn': lambda_function_arn,
                    'Input': json.dumps({
                        'user_id': user_id,
                        'tenant_id': tenant_id,
                        'subscription_id': subscription_id,
                        'expires_at': expires_at,
                        'notification_type': 'mark_expired'
                    })
                }
            ]
        )
        
        logger.info(f"Scheduled expiry marking for subscription {subscription_id}")
        
    except Exception as e:
        logger.error(f"Error scheduling expiry marking: {str(e)}")
        # Don't raise exception as this is not critical
